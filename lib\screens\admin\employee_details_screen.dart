import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../routes/app_routes.dart';

class EmployeeDetailsScreen extends StatefulWidget {
  final int employeeId;

  const EmployeeDetailsScreen({super.key, required this.employeeId});

  @override
  State<EmployeeDetailsScreen> createState() => _EmployeeDetailsScreenState();
}

class _EmployeeDetailsScreenState extends State<EmployeeDetailsScreen> {
  User? _employee;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadEmployeeDetails();
  }

  void _loadEmployeeDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Find employee in the provider
      final employeesProvider = context.read<EmployeesProvider>();
      await employeesProvider.loadEmployeesData();

      User? employee;
      try {
        employee = employeesProvider.employees
            .where((e) => e.id == widget.employeeId)
            .first;
      } catch (e) {
        employee = null;
      }

      setState(() {
        _employee = employee;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_employee?.name ?? 'تفاصيل الموظف', style: TextStyle(color: AppColors.textPrimary)),
        backgroundColor: AppColors.buttonBackground,
        foregroundColor: AppColors.buttonText,
        actions: [
          if (_employee != null)
            IconButton(
              icon: const Icon(Icons.edit, color: AppColors.textPrimary),
              onPressed: () => Navigator.pushNamed(
                context,
                AppRoutes.editEmployee,
                arguments: {'employee': _employee},
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator(color: AppColors.textPrimary))
          : _employee == null
          ? CustomErrorWidget(
              message: 'لم يتم العثور على الموظف',
              onRetry: _loadEmployeeDetails,
            )
          : _buildEmployeeDetails(),
    );
  }

  Widget _buildEmployeeDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildEmployeeHeader(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildPersonalInfo(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildEmployeeHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.buttonBackground, AppColors.background],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.buttonBackground.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: AppColors.buttonText.withOpacity(0.2),
            child: Text(
              _employee!.name.isNotEmpty
                  ? _employee!.name[0].toUpperCase()
                  : 'U',
              style: const TextStyle(
                color: AppColors.buttonText,
                fontSize: 36,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            _employee!.name,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.buttonText,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _employee!.email,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.buttonText.withOpacity(0.9),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.buttonText.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _getRoleText(_employee!.role),
              style: const TextStyle(
                color: AppColors.buttonText,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfo() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الملف الشخصي',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildInfoRow('الاسم الكامل', _employee!.name, Icons.person),
            _buildInfoRow('البريد الإلكتروني', _employee!.email, Icons.email),
            _buildInfoRow(
              'الدور',
              _getRoleText(_employee!.role),
              Icons.admin_panel_settings,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.editEmployee,
              arguments: {'employee': _employee},
            ),
            icon: const Icon(Icons.edit, color: AppColors.textPrimary),
            label: const Text('تعديل', style: TextStyle(color: AppColors.textPrimary)),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: AppColors.textPrimary),
            ),
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _showDeleteDialog,
            icon: const Icon(Icons.delete, color: AppColors.textPrimary),
            label: const Text('حذف', style: TextStyle(color: AppColors.textPrimary)),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.buttonText),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppColors.buttonText),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textPrimary),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  String _getRoleText(String role) {
    switch (role) {
      case 'admin':
        return 'مدير';
      case 'supervisor':
        return 'مشرف';
      case 'employee':
      default:
        return 'موظف';
    }
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.background,
        title: const Text('تأكيد الحذف', style: TextStyle(color: AppColors.textPrimary)),
        content: Text('هل أنت متأكد من حذف الموظف "${_employee!.name}"؟', style: TextStyle(color: AppColors.textPrimary)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: AppColors.buttonText)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteEmployee();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.buttonText),
            child: const Text('حذف', style: TextStyle(color: AppColors.textPrimary)),
          ),
        ],
      ),
    );
  }

  void _deleteEmployee() async {
    try {
      await context.read<EmployeesProvider>().deleteEmployee(_employee!.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الموظف بنجاح', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حذف الموظف: $e', style: TextStyle(color: AppColors.background)),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }
}