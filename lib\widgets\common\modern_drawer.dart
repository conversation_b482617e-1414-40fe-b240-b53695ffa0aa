import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../providers/auth_provider.dart';
import '../../routes/app_routes.dart';
import 'app_logo.dart';

class ModernDrawer extends StatelessWidget {
  final bool isAdmin;

  const ModernDrawer({super.key, this.isAdmin = false});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.background,
      child: Column(
        children: [
          _buildDrawerHeader(context),
          Expanded(child: _buildDrawerItems(context)),
          _buildDrawerFooter(context),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Container(
          height: 200,
          decoration:  BoxDecoration(gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight, colors: [
              AppColors.background,
              AppColors.background.withOpacity(0.7),
            ],
          )),
          child: <PERSON><PERSON><PERSON>(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const AppLogo(
                    type: LogoType.compact,
                    width: 120,
                    height: 40,
                    color: AppColors.textPrimary,
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 25,
                        backgroundColor: AppColors.textPrimary.withOpacity(0.2),
                        child: Text(
                          authProvider.userInitials,
                          style: const TextStyle(
                            color: AppColors.textPrimary,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              authProvider.displayName,
                              style: const TextStyle(
                                color: AppColors.textPrimary,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              authProvider.roleDisplayText,
                              style: TextStyle(
                                color: AppColors.textPrimary.withOpacity(0.8),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDrawerItems(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 8),
      children: [
        if (isAdmin)
          ..._buildAdminItems(context)
        else
          ..._buildUserItems(context),
        const Divider(),
        _buildDrawerItem(
          context,
          icon: Icons.settings,
          title: 'الإعدادات',
          onTap: () {
            Navigator.pop(context);
            // Navigate to settings
          },
        ),
        _buildDrawerItem(
          context,
          icon: Icons.help_outline,
          title: 'المساعدة',
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.about);
          },
        ),
      ],
    );
  }

  List<Widget> _buildAdminItems(BuildContext context) {
    return [
      _buildDrawerItem(
        context,
        icon: Icons.dashboard,
        title: 'لوحة التحكم',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushReplacementNamed(context, AppRoutes.adminDashboard);
        },
      ),
      _buildDrawerItem(
        context,
        icon: Icons.people,
        title: 'إدارة الموظفين',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.employeesManagement);
        },
      ),
      _buildDrawerItem(
        context,
        icon: Icons.location_on,
        title: 'إدارة المواقع',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.sitesManagement);
        },
      ),
      _buildDrawerItem(
        context,
        icon: Icons.assessment,
        title: 'التقارير',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.reportsScreen);
        },
      ),
      _buildDrawerItem(
        context,
        icon: Icons.monitor,
        title: 'المراقبة المباشرة',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.monitoringScreen);
        },
      ),
    ];
  }

  List<Widget> _buildUserItems(BuildContext context) {
    return [
      _buildDrawerItem(
        context,
        icon: Icons.access_time,
        title: 'تسجيل الحضور',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.attendanceScreen);
        },
      ),
      _buildDrawerItem(
        context,
        icon: Icons.person,
        title: 'الملف الشخصي',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.profileScreen);
        },
      ),
    ];
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppColors.textPrimary, size: 24),
      title: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
      ),
      onTap: onTap,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }

  Widget _buildDrawerFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppColors.textPrimary.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.logout, color: AppColors.buttonText),
            title: const Text(
              'تسجيل الخروج',
              style: TextStyle(
                color: AppColors.buttonText,
                fontWeight: FontWeight.w600,
              ),
            ),
            onTap: () => _showLogoutDialog(context),
            contentPadding: const EdgeInsets.symmetric(horizontal: 4),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج', style: TextStyle(color: AppColors.textPrimary)),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟', style: TextStyle(color: AppColors.textPrimary)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: AppColors.buttonText)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Close drawer
              context.read<AuthProvider>().logout();
              Navigator.pushReplacementNamed(context, AppRoutes.login);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.buttonText),
            child: const Text('تسجيل الخروج', style: TextStyle(color: AppColors.textPrimary)),
          ),
        ],
      ),
    );
  }
}