import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';

class AddEmployeeScreen extends StatefulWidget {
  const AddEmployeeScreen({super.key});

  @override
  State<AddEmployeeScreen> createState() => _AddEmployeeScreenState();
}

class _AddEmployeeScreenState extends State<AddEmployeeScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة موظف جديد', style: TextStyle(color: AppColors.textPrimary)),
        backgroundColor: AppColors.buttonBackground,
        foregroundColor: AppColors.buttonText,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: FormBuilder(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPersonalInfoSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildAccountInfoSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildRoleAndPermissionsSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الشخصية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderTextField(
              name: 'name',
              decoration: const InputDecoration(
                labelText: 'الاسم الكامل *',
                prefixIcon: Icon(Icons.person, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(errorText: 'الاسم مطلوب'),
                FormBuilderValidators.minLength(
                  2,
                  errorText: 'الاسم يجب أن يكون أكثر من حرفين',
                ),
              ]),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderTextField(
              name: 'phone',
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                prefixIcon: Icon(Icons.phone, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.numeric(
                  errorText: 'رقم الهاتف يجب أن يكون أرقام فقط',
                ),
              ]),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderTextField(
              name: 'national_id',
              decoration: const InputDecoration(
                labelText: 'رقم الهوية',
                prefixIcon: Icon(Icons.badge, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderDateTimePicker(
              name: 'birth_date',
              decoration: const InputDecoration(
                labelText: 'تاريخ الميلاد',
                prefixIcon: Icon(Icons.calendar_today, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              inputType: InputType.date,
              format: DateFormat('yyyy-MM-dd'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountInfoSection() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الحساب',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderTextField(
              name: 'email',
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني *',
                prefixIcon: Icon(Icons.email, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(
                  errorText: 'البريد الإلكتروني مطلوب',
                ),
                FormBuilderValidators.email(
                  errorText: 'البريد الإلكتروني غير صحيح',
                ),
              ]),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderTextField(
              name: 'password',
              decoration: const InputDecoration(
                labelText: 'كلمة المرور *',
                prefixIcon: Icon(Icons.lock, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              obscureText: true,
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(errorText: 'كلمة المرور مطلوبة'),
                FormBuilderValidators.minLength(
                  6,
                  errorText: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
                ),
              ]),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderTextField(
              name: 'confirm_password',
              decoration: const InputDecoration(
                labelText: 'تأكيد كلمة المرور *',
                prefixIcon: Icon(Icons.lock_outline, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              obscureText: true,
              validator: (value) {
                final password =
                    _formKey.currentState?.fields['password']?.value;
                if (value != password) {
                  return 'كلمة المرور غير متطابقة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleAndPermissionsSection() {
    return Card(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الدور والصلاحيات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderDropdown<String>(
              name: 'role',
              decoration: const InputDecoration(
                labelText: 'الدور *',
                prefixIcon: Icon(Icons.admin_panel_settings, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
              initialValue: 'employee',
              items: const [
                DropdownMenuItem(value: 'employee', child: Text('موظف', style: TextStyle(color: AppColors.textPrimary))),
                DropdownMenuItem(value: 'supervisor', child: Text('مشرف', style: TextStyle(color: AppColors.textPrimary))),
                DropdownMenuItem(value: 'admin', child: Text('مدير', style: TextStyle(color: AppColors.textPrimary))),
              ],
              validator: FormBuilderValidators.required(
                errorText: 'الدور مطلوب',
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Consumer<SitesProvider>(
              builder: (context, sitesProvider, child) {
                return FormBuilderDropdown<int>(
                  name: 'default_site_id',
                  decoration: const InputDecoration(
                    labelText: 'الموقع الافتراضي',
                    prefixIcon: Icon(Icons.location_on, color: AppColors.textPrimary),
                    border: OutlineInputBorder(),
                  ),
                  items: sitesProvider.sites.map((site) {
                    return DropdownMenuItem(
                      value: site.id,
                      child: Text(site.name, style: TextStyle(color: AppColors.textPrimary)),
                    );
                  }).toList(),
                );
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderTextField(
              name: 'employee_id',
              decoration: const InputDecoration(
                labelText: 'رقم الموظف',
                prefixIcon: Icon(Icons.numbers, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
                hintText: 'سيتم إنشاؤه تلقائياً إذا ترك فارغاً',
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderTextField(
              name: 'department',
              decoration: const InputDecoration(
                labelText: 'القسم',
                prefixIcon: Icon(Icons.business, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderTextField(
              name: 'position',
              decoration: const InputDecoration(
                labelText: 'المنصب',
                prefixIcon: Icon(Icons.work, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            FormBuilderTextField(
              name: 'salary',
              decoration: const InputDecoration(
                labelText: 'الراتب',
                prefixIcon: Icon(Icons.attach_money, color: AppColors.textPrimary),
                border: OutlineInputBorder(),
                suffixText: 'ريال',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.buttonText,
              side: const BorderSide(color: AppColors.buttonText),
            ),
            child: const Text('إلغاء', style: TextStyle(color: AppColors.textPrimary)),
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveEmployee,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
              foregroundColor: AppColors.buttonText,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2, color: AppColors.textPrimary),
                  )
                : const Text('حفظ', style: TextStyle(color: AppColors.textPrimary)),
          ),
        ),
      ],
    );
  }

  void _saveEmployee() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        final formData = _formKey.currentState!.value;

        await context.read<EmployeesProvider>().createEmployee(
          name: formData['name'],
          email: formData['email'],
          password: formData['password'],
          role: formData['role'] ?? 'employee',
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة الموظف بنجاح', style: TextStyle(color: AppColors.background)),
              backgroundColor: AppColors.buttonText,
            ),
          );
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في إضافة الموظف: $e', style: TextStyle(color: AppColors.background)),
              backgroundColor: AppColors.buttonText,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}